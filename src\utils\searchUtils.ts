import { ListMenuItem } from '@/types/Layout';

export interface SearchableMenuItem extends ListMenuItem {
  breadcrumb: string[];
  fullPath: string;
  searchableText: string;
}

export interface RouteDefinition {
  path: string;
  label: string;
  parentPath?: string;
  parentLabel?: string;
}

// Define all sub-routes for each main menu item
export const subRoutes: Record<string, RouteDefinition[]> = {
  '/academic-management': [
    { path: '/academic-management/year', label: 'Manage Year' },
    { path: '/academic-management/class', label: 'Manage Class' },
    { path: '/academic-management/class-section', label: 'Manage Section' },
    { path: '/academic-management/sort-class', label: 'Class Sort' },
    { path: '/academic-management/subject', label: 'Subject' },
    { path: '/academic-management/category-subject', label: 'Subject Category' },
    { path: '/academic-management/language-student', label: 'Language Student' },
    { path: '/academic-management/study-materials', label: 'Study Materials' },
    { path: '/academic-management/events', label: 'Event Details' },
  ],
  '/attendance-marking': [
    { path: '/attendance-marking/class-marking', label: 'Class Marking' },
    { path: '/attendance-marking/detailed-marking', label: 'Detailed Class Marking' },
    { path: '/attendance-marking/session-wise-marking', label: 'Session Wise Marking' },
    { path: '/attendance-marking/quick-marking', label: 'Quick Marking' },
    { path: '/attendance-marking/leave-note', label: 'Leave Note' },
    { path: '/attendance-marking/student-attendance-calendar', label: 'Student Attendance Calendar' },
    { path: '/attendance-marking/attendance-summary', label: 'Attendance Summary' },
    { path: '/attendance-marking/absentees-list', label: 'Absentees List' },
  ],
  '/message-box': [
    { path: '/message-box/quick-send', label: 'Quick Send' },
    { path: '/message-box/quick-send2', label: 'Quick Send 2' },
    { path: '/message-box/sms-template', label: 'SMS Template' },
    { path: '/message-box/send-others', label: 'Send to Others' },
    { path: '/message-box/delivery-report', label: 'Delivery Report' },
  ],
  '/voice-message': [
    { path: '/voice-message/create', label: 'Create Voice Message' },
    { path: '/voice-message/voice-template', label: 'Voice Template' },
    { path: '/voice-message/send-others', label: 'Send to Others' },
  ],
  '/app-notification': [
    { path: '/app-notification/create', label: 'Create Notification' },
    { path: '/app-notification/list', label: 'Notification List' },
    { path: '/app-notification/report', label: 'Notification Report' },
    { path: '/app-notification/staff-report', label: 'Staff Notification Report' },
  ],
  '/manage-students': [
    { path: '/manage-students/new', label: 'New Student' },
    { path: '/manage-students/info', label: 'Student Parent Info' },
    { path: '/manage-students/download-student-info', label: 'Download Student Info' },
    { path: '/manage-students/quick-update', label: 'Quick Update Student' },
    { path: '/manage-students/reallocate', label: 'Class Re-Allocation' },
    { path: '/manage-students/promote', label: 'Promote Students' },
    { path: '/manage-students/remarks', label: 'Students Remarks' },
    { path: '/manage-students/extracurricular', label: 'Students Extracurricular' },
  ],
  '/staff-management': [
    { path: '/staff-management/list', label: 'Manage Staffs' },
    { path: '/staff-management/allocation-list', label: 'CTS Allocation' },
    { path: '/staff-management/class-wise-allocation', label: 'Class Wise Allocation' },
    { path: '/staff-management/teacher-wise-allocation', label: 'Teacher Wise Allocation' },
    { path: '/staff-management/re-allocate', label: 'Staff Re-Allocation' },
    { path: '/staff-management/hours-list', label: 'Hours List' },
    { path: '/staff-management/staff-activity-report', label: 'Staff Activity Report' },
    { path: '/staff-management/category-list', label: 'Staff Category' },
    { path: '/staff-management/category-map', label: 'Staff Category Map' },
    { path: '/staff-management/conveyors', label: 'Conveyors' },
  ],
  '/staff-attendance': [
    { path: '/staff-attendance/leave-list', label: 'Leave List' },
    { path: '/staff-attendance/working-day-list', label: 'Staff Working Day List' },
    { path: '/staff-attendance/punch-time-set', label: 'Staff Punch Time Set' },
    { path: '/staff-attendance/punch-list', label: 'Punch List' },
    { path: '/staff-attendance/punch-report', label: 'Punch Report' },
  ],
  '/manage-fee': [
    { path: '/manage-fee/overview', label: 'Overview' },
    { path: '/manage-fee/basic-fee-list', label: 'Basic Fee List' },
    { path: '/manage-fee/term-fee-list', label: 'Term Fee List' },
    { path: '/manage-fee/collection', label: 'Fee Collection' },
    { path: '/manage-fee/pay-fee', label: 'Pay Fee' },
    { path: '/manage-fee/pay-fee-details', label: 'Pay Fee Details' },
    { path: '/manage-fee/total-paid-list', label: 'Total Paid List' },
    { path: '/manage-fee/basicfee-paid-list', label: 'Basic Fee Paid List' },
    { path: '/manage-fee/termfee-paid-list', label: 'Term Fee Paid List' },
    { path: '/manage-fee/daily-report', label: 'Daily Report' },
    { path: '/manage-fee/total-pending-list', label: 'Total Pending List' },
    { path: '/manage-fee/total-pending-basic-list', label: 'Basic Fee Pending List' },
    { path: '/manage-fee/total-pending-term-list', label: 'Term Fee Pending List' },
    { path: '/manage-fee/fee-setting', label: 'Fee Setting' },
    { path: '/manage-fee/fine-setting', label: 'Fine Setting' },
    { path: '/manage-fee/optional-fee', label: 'Optional Fee' },
    { path: '/manage-fee/fees-details', label: 'Fees Details' },
    { path: '/manage-fee/fee-date-settings', label: 'Fee Date Settings' },
    { path: '/manage-fee/optional-fee-settings', label: 'Optional Fee Setting' },
  ],
  '/manage-bus-fees': [
    { path: '/manage-bus-fees/bus-list', label: 'Bus List' },
    { path: '/manage-bus-fees/bus-stop-map', label: 'Bus Stop Map' },
    { path: '/manage-bus-fees/terms-list', label: 'Terms List' },
    { path: '/manage-bus-fees/term-fees-map', label: 'Term Fees Map' },
    { path: '/manage-bus-fees/term-student-map', label: 'Term Student Map' },
    { path: '/manage-bus-fees/pay-bus-fees', label: 'Pay Bus Fees' },
    { path: '/manage-bus-fees/paid-list', label: 'Bus Fee Paid List' },
    { path: '/manage-bus-fees/pending-list', label: 'Bus Fee Pending List' },
    { path: '/manage-bus-fees/fine-setting', label: 'Bus Fee Fine Setting' },
    { path: '/manage-bus-fees/optional', label: 'Bus Fee Optional' },
  ],
  '/fee-alert': [
    { path: '/fee-alert/update-fees', label: 'Update Fees' },
    { path: '/fee-alert/list', label: 'Fee Alert List' },
    { path: '/fee-alert/pending', label: 'Fee Alert Pending' },
  ],
  '/parent-enquiry': [{ path: '/parent-enquiry/list', label: 'Enquiry List' }],
  '/vehicle-tracking': [
    { path: '/vehicle-tracking/new', label: 'New Vehicle' },
    { path: '/vehicle-tracking/list', label: 'Vehicle List' },
  ],
  '/time-table': [
    { path: '/time-table/new', label: 'Create New Timetable' },
    { path: '/time-table/list', label: 'Timetable List' },
    { path: '/time-table/class-wise', label: 'Class Wise Timetable' },
    { path: '/time-table/staff-wise', label: 'Staff Wise Timetable' },
    { path: '/time-table/staff-attendance', label: 'Staff Attendance' },
    { path: '/time-table/staff-substitution', label: 'Staff Substitution' },
    { path: '/time-table/generator', label: 'Timetable Generator' },
  ],
  '/publish-result': [
    { path: '/publish-result/update', label: 'Update Result' },
    { path: '/publish-result/publish', label: 'Result Publish' },
  ],
  '/exam-center': [
    { path: '/exam-center/online-marks', label: 'Add Online Marks' },
    { path: '/exam-center/list', label: 'Manage Exam' },
    { path: '/exam-center/timetable', label: 'Exam Timetable' },
    { path: '/exam-center/send-timetable', label: 'Send Timetable' },
    { path: '/exam-center/mark-register', label: 'Mark Register' },
    { path: '/exam-center/mark-register-ce', label: 'Mark Register CE' },
    { path: '/exam-center/delete-marks', label: 'Delete Marks' },
    { path: '/exam-center/progress-report', label: 'Progress Report' },
    { path: '/exam-center/send-progress-report', label: 'Send Progress Report' },
  ],
  '/library-management': [
    { path: '/library-management/new-book', label: 'Enroll Books' },
    { path: '/library-management/book-list', label: 'Book List' },
    { path: '/library-management/issue-list', label: 'Issue List' },
    { path: '/library-management/category', label: 'Category Manager' },
    { path: '/library-management/location', label: 'Location Manager' },
    { path: '/library-management/fine-setting', label: 'Book Fine Setting' },
  ],
  '/store-management': [
    { path: '/store-management/product-list', label: 'Manage Products' },
    { path: '/store-management/map-product', label: 'Map Products' },
    { path: '/store-management/mapped-list', label: 'Mapped List' },
    { path: '/store-management/new-sale', label: 'Product Sale' },
    { path: '/store-management/sale-list', label: 'Sale List' },
    { path: '/store-management/daily-report', label: 'Store Daily Report' },
  ],

  '/report': [
    { path: '/report/student-details', label: 'Student Details' },
    { path: '/report/student-birthday-list', label: 'Student Birthday List' },
    { path: '/report/attendance-report', label: 'Attendance Report' },
    { path: '/report/absentees-list', label: 'Absentees List' },
    { path: '/report/teacher-subject-list', label: 'Teacher Subject List' },
    { path: '/report/class-teacher-info', label: 'Class Teacher Info' },
    { path: '/report/pta-members-list', label: 'PTA Members List' },
    { path: '/report/staff-list', label: 'Staff List' },
    { path: '/report/conveyors-list', label: 'Conveyors List' },
  ],
  '/admin-controls': [
    { path: '/admin-controls/send-updates', label: 'Send Updates' },
    { path: '/admin-controls/staff-login', label: 'Staff Login List' },
    { path: '/admin-controls/parent-login', label: 'Parent Login' },
    { path: '/admin-controls/app-details', label: 'App Details List' },
    { path: '/admin-controls/app-update-message', label: 'App Update Message' },
    { path: '/admin-controls/mail-template', label: 'Mail Template' },
  ],
  '/general-settings': [
    { path: '/general-settings/about-us', label: 'About Us' },
    { path: '/general-settings/contact-us', label: 'Contact Us' },
    { path: '/general-settings/landing-images-list', label: 'Landing Images List' },
    { path: '/general-settings/authorise-keywords', label: 'Authorise Keywords' },
  ],
  '/group-settings': [
    { path: '/group-settings/groups', label: 'Groups' },
    { path: '/group-settings/student-members', label: 'Student Members' },
    { path: '/group-settings/staff-members', label: 'Staff Members' },
    { path: '/group-settings/public-members', label: 'Public Members' },
  ],
  '/live-class': [
    { path: '/live-class/schedule-list', label: 'Schedule List' },
    { path: '/live-class/live-class-list', label: 'Live Class List' },
    { path: '/live-class/zoom-key-list', label: 'Zoom Key List' },
  ],
  '/school-calendar': [
    { path: '/school-calendar/calendar', label: 'Calendar' },
    { path: '/school-calendar/holidays-list', label: 'Holidays List' },
    { path: '/school-calendar/activities-list', label: 'Activities List' },
  ],
  '/online-video-class': [
    { path: '/online-video-class/student-device-details', label: 'Student Device Details' },
    { path: '/online-video-class/student-block-unblock', label: 'Student Block Unblock' },
    { path: '/online-video-class/student-block-list', label: 'Student Block List' },
    { path: '/online-video-class/student-block-report', label: 'Student Block Report' },
    { path: '/online-video-class/video-detail', label: 'Video Detail' },
    { path: '/online-video-class/subject-chapter-details', label: 'Subject Chapter Details' },
  ],
  '/assignment': [
    { path: '/assignment/assignment-list', label: 'Assignment List' },
    { path: '/assignment/map-progress-card', label: 'Map to Progress Card' },
    { path: '/assignment/zoom-key-list', label: 'Zoom Key List' },
  ],
  '/online-exam': [
    { path: '/online-exam/exam-details', label: 'Exam Details' },
    { path: '/online-exam/questions-list', label: 'Questions List' },
    { path: '/online-exam/map-progress-card', label: 'Map to Progress Card' },
  ],
  '/pta-forum': [
    { path: '/pta-forum/members-list', label: 'Members List' },
    { path: '/pta-forum/core-committee', label: 'Core Committee' },
    { path: '/pta-forum/trust-committee', label: 'Trust Committee' },
  ],
  '/certificates': [
    { path: '/certificates/TC-list', label: 'TC List' },
    { path: '/certificates/CC-list', label: 'CC List' },
    { path: '/certificates/bonafide-list', label: 'Bonafide List' },
  ],
  '/tc-cc': [
    { path: '/tc-cc/tc-list', label: 'TC List' },
    { path: '/tc-cc/cc-list', label: 'CC List' },
  ],

  '/school-plan': [
    { path: '/school-plan/session-plan-list', label: 'Session Plan List' },
    { path: '/school-plan/lesson-plan-list', label: 'Lesson Plan List' },
    { path: '/school-plan/annual-plan-list', label: 'Annual Plan List' },
  ],
  '/album': [
    { path: '/album/full-list', label: 'Full List' },
    { path: '/album/photo-list', label: 'Photo List' },
    { path: '/album/video-list', label: 'Video List' },
  ],
  '/payments': [
    { path: '/payments/category', label: 'Payment Category' },
    { path: '/payments/sub-category', label: 'Payment Sub Category' },
    { path: '/payments/new', label: 'Create Payment' },
    { path: '/payments/list', label: 'Payment List' },
  ],
  // '/social': [{ path: '/social', label: 'Social' }],
  '/registration': [
    { path: '/registration/new-student', label: 'New Student Registration' },
    { path: '/registration/list', label: 'Registration List' },
    { path: '/registration/pending', label: 'Pending Registrations' },
    { path: '/registration/approved', label: 'Approved Registrations' },
  ],
};

/**
 * Creates searchable menu items from the main menu data and sub-routes
 */
export function createSearchableMenuItems(menuData: ListMenuItem[]): SearchableMenuItem[] {
  const searchableItems: SearchableMenuItem[] = [];

  // Add main menu items
  menuData.forEach((item) => {
    const searchableItem: SearchableMenuItem = {
      ...item,
      breadcrumb: [item.label],
      fullPath: item.url || item.link || '',
      searchableText: item.label.toLowerCase(),
    };
    searchableItems.push(searchableItem);

    // Add sub-routes for this main menu item
    const itemSubRoutes = subRoutes[item.link || ''];
    if (itemSubRoutes) {
      itemSubRoutes.forEach((subRoute) => {
        const subItem: SearchableMenuItem = {
          id: `${item.id}-${subRoute.path.split('/').pop()}`,
          label: subRoute.label,
          icon: item.icon,
          link: subRoute.path,
          url: subRoute.path,
          breadcrumb: [item.label, subRoute.label],
          fullPath: subRoute.path,
          searchableText: `${item.label} ${subRoute.label}`.toLowerCase(),
        };
        searchableItems.push(subItem);
      });
    }
  });

  return searchableItems;
}

/**
 * Filters searchable menu items based on search query
 */
export function filterMenuItems(searchableItems: SearchableMenuItem[], query: string): SearchableMenuItem[] {
  if (!query.trim()) {
    return [];
  }

  const normalizedQuery = query.toLowerCase().trim();

  return searchableItems.filter((item) => {
    return item.searchableText.includes(normalizedQuery);
  });
}

/**
 * Highlights matching text in a string
 */
export function highlightText(text: string, query: string): string {
  if (!query.trim()) {
    return text;
  }

  const normalizedQuery = query.toLowerCase().trim();
  const regex = new RegExp(`(${normalizedQuery})`, 'gi');

  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(func: T, delay: number): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}
